const mysql = require('mysql2/promise');
const dotenv = require('dotenv');
dotenv.config();

const {
  DB_HOST,
  DB_USER,
  DB_PASSWORD,
  DB_NAME,
  DB_PORT
} = process.env;

// Validate required environment variables
if (!DB_HOST || !DB_USER || !DB_PASSWORD || !DB_NAME) {
  console.error('❌ Missing required database environment variables');
  console.error('Required: DB_HOST, DB_USER, DB_PASSWORD, DB_NAME');
  process.exit(1);
}

const initDB = async () => {
  try {
    // Connect without selecting DB
    const connection = await mysql.createConnection({
      host: DB_HOST,
      user: DB_USER,
      password: DB_PASSWORD,
      port: DB_PORT || 3306
    });

    // Create database if not exists
    await connection.query(`CREATE DATABASE IF NOT EXISTS \`${DB_NAME}\``);
    console.log(`✅ Database '${DB_NAME}' is ready.`);

    await connection.end();
  } catch (err) {
    console.error('❌ Error creating database:', err.message);
    process.exit(1); // Stop process if DB creation fails
  }
};

initDB();
