{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"init-db": "node initDB.js", "start": "node index.js", "dev": "nodemon index.js", "start-safe": "node initDB.js && node index.js", "test": "node test-api.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.2", "nanoid": "^3.3.4"}, "devDependencies": {"axios": "^1.10.0", "nodemon": "^3.1.10"}}