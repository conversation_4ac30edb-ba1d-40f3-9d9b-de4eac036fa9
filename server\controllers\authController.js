const db = require('../db');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// ENV for JWT secret
const JWT_SECRET = process.env.JWT_SECRET || 'supersecretkey';
const JWT_EXPIRES_IN = '7d'; // or 1d, 30m etc

// Utility to send cookie
const sendToken = (res, userId) => {
  const token = jwt.sign({ id: userId }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

  res.cookie('token', token, {
    httpOnly: true,
    secure: false, // true in production with HTTPS
    sameSite: 'lax',
    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
  });
};

// POST /api/signup
const signup = async (req, res) => {
  const { username, email, password } = req.body;

  if (!username || !email || !password)
    return res.status(400).json({ message: 'All fields are required.' });

  try {
    const [existing] = await db.execute('SELECT id FROM users WHERE email = ?', [email]);
    if (existing.length > 0)
      return res.status(409).json({ message: 'User already exists.' });

    const hashed = await bcrypt.hash(password, 10);

    const [result] = await db.execute(
      'INSERT INTO users (username, email, password) VALUES (?, ?, ?)',
      [username, email, hashed]
    );

    sendToken(res, result.insertId);
    res.status(201).json({ message: 'User registered successfully.' });
  } catch (err) {
    console.error('Signup error:', err);
    res.status(500).json({ message: 'Server error during signup.' });
  }
};

// POST /api/login
const login = async (req, res) => {
  console.log(req);
  const { email, password } = req.body; 
  

  if (!email || !password)
    return res.status(400).json({ message: 'Email and password are required.' });

  try {
    const [users] = await db.execute('SELECT * FROM users WHERE email = ?', [email]);
    const user = users[0];

    if (!user) return res.status(401).json({ message: 'Invalid credentials.' });

    const match = await bcrypt.compare(password, user.password);
    if (!match) return res.status(401).json({ message: 'Invalid credentials.' });

    sendToken(res, user.id);
    res.json({ message: 'Logged in successfully.' });
  } catch (err) {
    console.error('Login error:', err);
    res.status(500).json({ message: 'Server error during login.' });
  }
};

// GET /api/logout
const logout = (req, res) => {
  res.clearCookie('token', { httpOnly: true });
  res.json({ message: 'Logged out successfully.' });
};

// GET /api/me
const getMe = async (req, res) => {
  try {
    const userId = req.user.id;
    const [users] = await db.execute('SELECT id, username, email, created_at FROM users WHERE id = ?', [userId]);
    res.json(users[0]);
  } catch (err) {
    console.error('GetMe error:', err);
    res.status(500).json({ message: 'Failed to fetch user.' });
  }
};

module.exports = {
  signup,
  login,
  logout,
  getMe,
};
