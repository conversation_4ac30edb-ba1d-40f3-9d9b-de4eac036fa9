const { createShortUrl, getOriginalUrl, incrementClick, checkCodeExists, getUrlInfo } = require('../models/urlModel');
const { nanoid } = require('nanoid');

const BASE_URL = process.env.BASE_URL || 'http://localhost:5000';

// POST /api/shorten
const shortenUrl = async (req, res) => {
  const { original_url, custom_code, expires_at } = req.body;
  const user_id = req.user.id;

  if (!original_url) return res.status(400).json({ message: 'Original URL is required.' });

  // URL validation
  try {
    new URL(original_url);
  } catch (error) {
    return res.status(400).json({ message: 'Please provide a valid URL.' });
  }

  let short_code = custom_code?.trim() || nanoid(6);

  try {
    const exists = await checkCodeExists(short_code);
    if (exists) return res.status(409).json({ message: 'Short code already taken.' });

    await createShortUrl({ original_url, short_code, expires_at, user_id });

    res.status(201).json({
      short_url: `${BASE_URL}/${short_code}`
    });
  } catch (err) {
    console.error('Shorten error:', err);
    res.status(500).json({ message: 'Failed to shorten URL.' });
  }
};

// GET /:shortCode
const redirectUrl = async (req, res) => {
  const { shortCode } = req.params;

  try {
    const urlData = await getOriginalUrl(shortCode);
    if (!urlData) return res.status(404).json({ message: 'Short URL not found.' });

    const now = new Date();
    if (urlData.expires_at && new Date(urlData.expires_at) < now)
      return res.status(410).json({ message: 'Link has expired.' });

    await incrementClick(shortCode);
    res.redirect(urlData.original_url);
  } catch (err) {
    console.error('Redirect error:', err);
    res.status(500).json({ message: 'Failed to redirect.' });
  }
};

// GET /api/info/:shortCode
const getUrlStats = async (req, res) => {
  const { shortCode } = req.params;

  try {
    const data = await getUrlInfo(shortCode);
    if (!data) return res.status(404).json({ message: 'URL not found.' });

    res.json(data);
  } catch (err) {
    console.error('Stats error:', err);
    res.status(500).json({ message: 'Failed to fetch URL info.' });
  }
};

module.exports = {
  shortenUrl,
  redirectUrl,
  getUrlStats,
};
