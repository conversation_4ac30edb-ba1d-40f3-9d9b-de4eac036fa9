const { db } = require('../db');

// ✅ Create a new short URL
const createShortUrl = async ({ original_url, short_code, expires_at, user_id }) => {
  const [result] = await db.execute(
    `INSERT INTO urls (original_url, short_code, expires_at, user_id)
     VALUES (?, ?, ?, ?)`,
    [original_url, short_code, expires_at || null, user_id]
  );
  return result.insertId;
};

// ✅ Get original URL from short code
const getOriginalUrl = async (short_code) => {
  const [rows] = await db.execute(
    `SELECT * FROM urls WHERE short_code = ? LIMIT 1`,
    [short_code]
  );
  return rows[0];
};

// ✅ Increment click count
const incrementClick = async (short_code) => {
  await db.execute(
    `UPDATE urls SET clicks = clicks + 1 WHERE short_code = ?`,
    [short_code]
  );
};

// ✅ Check if custom short code already exists
const checkCodeExists = async (short_code) => {
  const [rows] = await db.execute(
    `SELECT id FROM urls WHERE short_code = ? LIMIT 1`,
    [short_code]
  );
  return rows.length > 0;
};

// ✅ Get stats for a short URL (optional)
const getUrlInfo = async (short_code) => {
  const [rows] = await db.execute(
    `SELECT original_url, clicks, created_at, expires_at
     FROM urls WHERE short_code = ? LIMIT 1`,
    [short_code]
  );
  return rows[0];
};

module.exports = {
  createShortUrl,
  getOriginalUrl,
  incrementClick,
  checkCodeExists,
  getUrlInfo,
};
