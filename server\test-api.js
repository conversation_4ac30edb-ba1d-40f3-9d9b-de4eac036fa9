const axios = require('axios');

const BASE_URL = 'http://localhost:5000';

// Test data
const testUser = {
  username: 'testuser',
  email: '<EMAIL>',
  password: 'testpassword123'
};

const testUrl = {
  original_url: 'https://www.google.com',
  custom_code: 'google'
};

// Helper function to make requests with cookies
let cookies = '';

const makeRequest = async (method, endpoint, data = null) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies
      }
    };
    
    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    
    // Extract cookies from response
    if (response.headers['set-cookie']) {
      cookies = response.headers['set-cookie'].join('; ');
    }
    
    return response.data;
  } catch (error) {
    console.error(`❌ ${method} ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
};

const runTests = async () => {
  console.log('🧪 Starting API Tests...\n');

  try {
    // Test 1: Server health check
    console.log('1. Testing server health...');
    const health = await makeRequest('GET', '/');
    console.log('✅ Server is running:', health);

    // Test 2: User signup
    console.log('\n2. Testing user signup...');
    const signupResult = await makeRequest('POST', '/api/signup', testUser);
    console.log('✅ Signup successful:', signupResult);

    // Test 3: User login
    console.log('\n3. Testing user login...');
    const loginResult = await makeRequest('POST', '/api/login', {
      email: testUser.email,
      password: testUser.password
    });
    console.log('✅ Login successful:', loginResult);

    // Test 4: Get user profile
    console.log('\n4. Testing get user profile...');
    const profile = await makeRequest('GET', '/api/me');
    console.log('✅ Profile retrieved:', profile);

    // Test 5: Shorten URL
    console.log('\n5. Testing URL shortening...');
    const shortenResult = await makeRequest('POST', '/api/shorten', testUrl);
    console.log('✅ URL shortened:', shortenResult);

    // Test 6: Get URL stats
    console.log('\n6. Testing URL stats...');
    const stats = await makeRequest('GET', `/api/info/${testUrl.custom_code}`);
    console.log('✅ URL stats retrieved:', stats);

    // Test 7: User logout
    console.log('\n7. Testing user logout...');
    const logoutResult = await makeRequest('GET', '/api/logout');
    console.log('✅ Logout successful:', logoutResult);

    console.log('\n🎉 All tests passed!');

  } catch (error) {
    console.error('\n💥 Test failed:', error.message);
    process.exit(1);
  }
};

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
