# URL Shortener API

A secure URL shortening service built with Node.js, Express, and MySQL.

## 🚀 Features

- User authentication (signup, login, logout)
- JWT-based session management
- URL shortening with custom codes
- Click tracking and analytics
- URL expiration support
- Secure cookie handling
- Input validation and sanitization

## 🛠️ Setup Instructions

### 1. Prerequisites

- Node.js (v14 or higher)
- MySQL (v5.7 or higher)
- npm or yarn

### 2. Installation

```bash
# Clone the repository
cd server

# Install dependencies
npm install

# Copy environment file
cp .env.example .env
```

### 3. Environment Configuration

Edit the `.env` file with your database credentials:

```env
# Database Configuration
DB_HOST=localhost
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=url_shortener
DB_PORT=3306

# JWT Configuration (IMPORTANT: Use a strong secret in production)
JWT_SECRET=your_super_secure_jwt_secret_key_here_minimum_32_characters

# Server Configuration
PORT=5000
BASE_URL=http://localhost:5000

# Frontend URL (for CORS in production)
FRONTEND_URL=http://localhost:3000

# Environment
NODE_ENV=development
```

### 4. Database Setup

```bash
# Initialize database and tables
npm run init-db
```

### 5. Start the Server

```bash
# Development mode (with auto-reload)
npm run dev

# Production mode
npm start

# Safe start (initializes DB first)
npm run start-safe
```

## 🧪 Testing

Run the API tests to verify everything is working:

```bash
npm test
```

## 📚 API Endpoints

### Authentication
- `POST /api/signup` - Register a new user
- `POST /api/login` - Login user
- `GET /api/logout` - Logout user
- `GET /api/me` - Get current user profile

### URL Management
- `POST /api/shorten` - Shorten a URL (requires authentication)
- `GET /api/info/:shortCode` - Get URL statistics
- `GET /:shortCode` - Redirect to original URL

## 🔒 Security Features

- Password hashing with bcrypt
- JWT token authentication
- HTTP-only cookies
- CORS protection
- Input validation
- SQL injection prevention
- Environment-based security settings

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Verify MySQL is running
   - Check database credentials in `.env`
   - Ensure database exists

2. **JWT Secret Error**
   - Make sure `JWT_SECRET` is set in `.env`
   - Use a strong secret (minimum 32 characters)

3. **CORS Issues**
   - Check `FRONTEND_URL` in `.env`
   - Verify origin settings in `index.js`

4. **Port Already in Use**
   - Change `PORT` in `.env`
   - Kill existing processes on the port

## 📝 Development Notes

- The server automatically creates database tables on startup
- All passwords are hashed before storage
- JWT tokens expire after 7 days
- URLs can have custom short codes
- Click tracking is automatic
- URLs can have expiration dates
